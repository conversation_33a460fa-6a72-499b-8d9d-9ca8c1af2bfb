// src/lib/actions/availability.actions.ts
'use server';

import { revalidatePath } from 'next/cache';
// import { auth } from '@clerk/nextjs/server'; // Clerk kaldırıldı
import prisma from '@/lib/prisma';
import { AvailabilitySlot } from '@prisma/client';

// Yeni bir müsaitlik slotu oluşturma
export async function createAvailabilitySlot(
  startTime: Date,
  endTime: Date
): Promise<{ success: boolean; error?: string; slot?: AvailabilitySlot }> {
  // const { userId } = auth(); // Clerk kaldırıldı
  let userId = null;

  if ((userId === null || userId === undefined) && process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: createAvailabilitySlot - Using default 'test-teacher-id'.");
    userId = "test-teacher-id"; // Test için varsayılan öğretmen ID'si
  }

  if (!userId) {
    return { success: false, error: 'Yet<PERSON>iz işlem. Kullanıcı ID bulunamadı.' };
  }

  // TODO: Gelen tarihlerin geçerliliğini kontrol et (geçmiş tarih olmamalı vb.)
  // TODO: Slotların çakışıp çakışmadığını kontrol et (isteğe bağlı, selectOverlap zaten engelliyor)

  try {
    const newSlot = await prisma.availabilitySlot.create({
      data: {
        teacherId: userId, // Clerk'ten gelen userId yerine mock veya parametre ile gelen ID kullanılmalı
        startTime: startTime,
        endTime: endTime,
        isBooked: false,
      },
    });
    revalidatePath('/teacher/profile');
    return { success: true, slot: newSlot };
  } catch (error) {
    // Removed console.error - return error result instead
    return { success: false, error: 'Müsaitlik eklenirken bir hata oluştu.' };
  }
}

// Bir müsaitlik slotunu silme
export async function deleteAvailabilitySlot(
  slotId: string
): Promise<{ success: boolean; error?: string }> {
  // const { userId } = auth(); // Clerk kaldırıldı
  let userId = null;

  if ((userId === null || userId === undefined) && process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: deleteAvailabilitySlot - Using default 'test-teacher-id'.");
    userId = "test-teacher-id"; // Test için varsayılan öğretmen ID'si
  }

  if (!userId) {
    return { success: false, error: 'Yetkisiz işlem. Kullanıcı ID bulunamadı.' };
  }

  try {
    // Slotun silmeye çalışan öğretmene ait olduğunu doğrula
    const slot = await prisma.availabilitySlot.findUnique({
      where: { id: slotId },
    });

    if (!slot || slot.teacherId !== userId) { // Clerk'ten gelen userId yerine mock veya parametre ile gelen ID kullanılmalı
      return { success: false, error: 'Slot bulunamadı veya size ait değil.' };
    }

    // TODO: Eğer slot rezerve edilmişse (isBooked=true) silinmeli mi? Karar verilmeli.
    // Şimdilik rezerve edilmiş olsa bile silmeye izin veriyoruz.

    await prisma.availabilitySlot.delete({
      where: { id: slotId },
    });

    revalidatePath('/teacher/profile');
    return { success: true };
  } catch (error) {
    // Removed console.error - return error result instead
    return { success: false, error: 'Müsaitlik silinirken bir hata oluştu.' };
  }
}

// Belirli bir öğretmenin tüm müsaitlik slotlarını getirme
export async function getAvailabilitySlots(
  teacherId: string
): Promise<AvailabilitySlot[]> {
   // Bu fonksiyonun çağrıldığı yerde yetki kontrolü yapıldığı varsayılır
   // (Örn: Sadece kendi profilini gören öğretmen veya admin)
  try {
    const slots = await prisma.availabilitySlot.findMany({
      where: {
        teacherId: teacherId,
      },
      orderBy: {
        startTime: 'asc',
      },
    });
    return slots;
  } catch (error) {
    // Removed console.error - return empty array for error cases
    return [];
  }
}