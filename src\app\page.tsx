"use client"

import { useState, lazy, Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useLanguage } from "@/lib/i18n/LanguageContext"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle, MessageSquarePlus } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog"

// Lazy loading ile performans optimizasyonu
const TeacherList = lazy(() => import('@/components/TeacherList'))
const Features = lazy(() => import('@/components/Features'))
const CourseList = lazy(() => import('@/components/CourseList'))
const Testimonials = lazy(() => import('@/components/Testimonials').then(module => ({ default: module.Testimonials })))
const ModernContactForm = lazy(() => import('@/app/iletisim/page').then(module => ({ default: module.ModernContactForm })))
const IletisimBilgileriKarti = lazy(() => import('@/components/IletisimBilgileriKarti').then(module => ({ default: module.IletisimBilgileriKarti })))

// Loading bileşenleri
const LoadingSpinner = () => (
  <div className="flex justify-center items-center py-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function HomePage() {
  const router = useRouter()
  const { t } = useLanguage()
  const [email, setEmail] = useState("")
  const [fullName, setFullName] = useState("")

  const handleFormNavigation = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Kullanıcı bilgilerini localStorage'a kaydet
    if (email) localStorage.setItem("userEmail", email)
    if (fullName) localStorage.setItem("userName", fullName)
    
    router.push("/seviye-testi")
  }

  return (<>
    {/* Hero Section */}
    <section className="relative bg-gradient-to-b from-gray-50/80 to-white overflow-hidden">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Left Column - Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-8"
          >
            <h1 className="text-[28px] sm:text-[42px] font-bold text-gray-900 leading-tight">
<span className="font-black">Almanca</span> öğrenmek <span className="font-black">ABC</span> kadar kolay!
</h1>
            <p className="text-base text-gray-600 max-w-lg">
            Online Almanca Kursu ile Almanca&apos;yı hızla ve etkili bir şekilde öğrenin! İnteraktif alıştırmalar, uzman eğitmenlerle online dersler ve canlı konuşma pratiği sayesinde Almanca akıcılığına kısa sürede ulaşın. Hemen başlayın!
            </p>
            <div className="flex flex-wrap gap-4">
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90">
                <Link href="/ogretmenler">
                  Öğretmen Bul
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline">
                <Link href="/nasil-calisir">
                  Öğretmen Ol
                </Link>
              </Button>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span className="flex items-center">
                <CheckCircle className="text-green-500 mr-2 h-5 w-5" />7 Gün Ücretsiz Deneme
              </span>
              <span className="flex items-center">
                <CheckCircle className="text-green-500 mr-2 h-5 w-5" />
                Para İade Garantisi
              </span>
            </div>
          </motion.div>

          {/* Right Column - Sign Up Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="backdrop-blur bg-white/90 shadow-md rounded-md border border-gray-200 max-w-sm md:max-w-md w-full">
              <CardContent className="p-3 sm:p-4">
                <h2 className="text-md sm:text-lg font-semibold mb-2 text-center">Ücretsiz Almanca Seviye Tespit Sınavı</h2>
                <form className="space-y-2" onSubmit={handleFormNavigation}>
                  <div className="relative">
                    <input
                      type="text"
                      name="fullName"
                      id="fullName"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      className="w-full px-3 py-1.5 pt-5 border rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent peer text-sm"
                      required
                      placeholder=" "
                    />
                    <label
                      htmlFor="fullName"
                      className="absolute left-3 top-1.5 text-muted-foreground text-xs transition-all pointer-events-none peer-placeholder-shown:top-3 peer-placeholder-shown:text-sm peer-focus:top-1.5 peer-focus:text-xs"
                    >
                      {t("hero.fullName")}
                    </label>
                  </div>
                  <div className="relative">
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-1.5 pt-5 border rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent peer text-sm"
                      required
                      placeholder=" "
                    />
                    <label
                      htmlFor="email"
                      className="absolute left-3 top-1.5 text-muted-foreground text-xs transition-all pointer-events-none peer-placeholder-shown:top-3 peer-placeholder-shown:text-sm peer-focus:top-1.5 peer-focus:text-xs"
                    >
                      {t("hero.email")}
                    </label>
                  </div>
                  <Button type="submit" className="w-full h-9 text-xs bg-primary hover:bg-primary/90 text-white rounded-md">
                    {t("hero.startNow")}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Statistics Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-16"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {[
              { number: "10K+", label: "Aktif Öğrenci" },
              { number: "500+", label: "Uzman Öğretmen" },
              { number: "50+", label: "Ülkeden Katılım" },
              { number: "4.9/5", label: "Öğrenci Memnuniyeti" },
            ].map((stat, index) => (
              <div key={index} className="space-y-2">
                <div className="text-4xl font-bold text-primary">{stat.number}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
    {/* Rest of the sections */}
    <div className="theme-transition">
      <Suspense fallback={<LoadingSpinner />}>
        <TeacherList />
      </Suspense>
      <Suspense fallback={<LoadingSpinner />}>
        <CourseList />
      </Suspense>
      <Suspense fallback={<LoadingSpinner />}>
        <Features />
      </Suspense>
      <Suspense fallback={<LoadingSpinner />}>
        <Testimonials />
      </Suspense>
      {/* İletişim Formu (Popup) Bölümü - Yardım sayfasından güncellendi */}
      <section className="py-8 md:py-12 bg-slate-50 dark:bg-slate-900 mt-16 rounded-lg">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Bizimle İletişime Geçin
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl mx-auto">
              Sorularınız mı var veya yardıma mı ihtiyacınız var? Hızlıca mesaj gönderebilir veya diğer tüm iletişim seçenekleri için <Link href="/iletisim" className="text-sky-600 hover:underline dark:text-sky-400">iletişim sayfamızı</Link> ya da <Link href="/yardim" className="text-sky-600 hover:underline dark:text-sky-400">yardım merkezimizi</Link> ziyaret edebilirsiniz.
            </p>
            <Dialog>
              <DialogTrigger asChild>
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-white dark:bg-sky-600 dark:hover:bg-sky-700">
                  <MessageSquarePlus className="mr-2 h-5 w-5" />
                  Mesaj Gönder
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-4xl dark:bg-slate-800 p-0">
                <DialogHeader className="p-6 pb-4">
                  <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">Bize Ulaşın</DialogTitle>
                  <DialogDescription className="text-sm text-gray-600 dark:text-gray-300">
                    Aşağıdaki formu doldurarak veya iletişim bilgilerimizi kullanarak bize ulaşabilirsiniz.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-1 lg:grid-cols-5 overflow-hidden rounded-b-lg">
                  <Suspense fallback={<LoadingSpinner />}>
                    <IletisimBilgileriKarti />
                  </Suspense>
                  <div className="p-6 md:p-8 lg:col-span-3 bg-white dark:bg-slate-800">
                    <Suspense fallback={<LoadingSpinner />}>
                      <ModernContactForm />
                    </Suspense>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </motion.div>
        </div>
      </section>
    </div>
  </>);
}
