"use server";

import { stripe, toStripeAmount, calculateCommission } from "@/lib/stripe";
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import Stripe from "stripe";

interface CreatePaymentIntentParams {
  bookingId: string;
  amount: number;
  currency?: string;
  metadata?: Record<string, string>;
}

interface PaymentIntentResult {
  success: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  error?: string;
}

// PaymentIntent oluşturma
export async function createPaymentIntent({
  bookingId,
  amount,
  currency = "try",
  metadata = {},
}: CreatePaymentIntentParams): Promise<PaymentIntentResult> {
  try {
    // Booking'i kontrol et
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        student: true,
        teacher: true,
        payment: true,
      },
    });

    if (!booking) {
      return {
        success: false,
        error: "Rezervasyon bulunamadı.",
      };
    }

    // Zaten ödeme yapılmış mı kontrol et
    if (booking.payment) {
      return {
        success: false,
        error: "Bu rezervasyon için zaten ödeme yapılmış.",
      };
    }

    // Komisyon hesapla
    const commissionAmount = calculateCommission(amount);
    const stripeAmount = toStripeAmount(amount);

    // PaymentIntent oluştur
    const paymentIntent = await stripe.paymentIntents.create({
      amount: stripeAmount,
      currency: currency.toLowerCase(),
      payment_method_types: ["card"],
      metadata: {
        bookingId,
        studentId: booking.studentId,
        teacherId: booking.teacherId,
        commissionAmount: commissionAmount.toString(),
        ...metadata,
      },
      description: `AlmancaABC - ${booking.teacher.firstName} ${booking.teacher.lastName} ile ders rezervasyonu`,
    });

    // Payment kaydı oluştur
    await prisma.payment.create({
      data: {
        stripePaymentIntentId: paymentIntent.id,
        amount: amount,
        currency: currency.toUpperCase(),
        status: "pending",
        bookingId: bookingId,
      },
    });

    return {
      success: true,
      clientSecret: paymentIntent.client_secret!,
      paymentIntentId: paymentIntent.id,
    };
  } catch (error) {
    console.error("PaymentIntent oluşturma hatası:", error);
    return {
      success: false,
      error: "Ödeme işlemi başlatılırken bir hata oluştu.",
    };
  }
}

// Ödeme durumunu güncelleme
export async function updatePaymentStatus(
  paymentIntentId: string,
  status: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Stripe'dan PaymentIntent'i al
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    // Payment kaydını güncelle
    const payment = await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntentId },
      data: { status },
      include: { booking: true },
    });

    // Ödeme başarılıysa booking durumunu güncelle
    if (status === "succeeded") {
      await prisma.booking.update({
        where: { id: payment.bookingId },
        data: {
          status: "CONFIRMED",
          commissionRate: 0.15, // %15 komisyon
          commissionFee: calculateCommission(payment.amount.toNumber()),
        },
      });

      // Availability slot'u rezerve edilmiş olarak işaretle
      await prisma.availabilitySlot.update({
        where: { id: payment.booking.availabilitySlotId },
        data: { isBooked: true },
      });
    }

    revalidatePath("/dashboard");
    revalidatePath("/teacher");
    revalidatePath("/student");

    return { success: true };
  } catch (error) {
    console.error("Ödeme durumu güncelleme hatası:", error);
    return {
      success: false,
      error: "Ödeme durumu güncellenirken bir hata oluştu.",
    };
  }
}

// Ödeme geçmişini getirme
export async function getPaymentHistory(userId: string, userType: "student" | "teacher") {
  try {
    const whereClause = userType === "student" 
      ? { booking: { studentId: userId } }
      : { booking: { teacherId: userId } };

    const payments = await prisma.payment.findMany({
      where: whereClause,
      include: {
        booking: {
          include: {
            student: true,
            teacher: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
    });

    return payments;
  } catch (error) {
    console.error("Ödeme geçmişi getirme hatası:", error);
    return [];
  }
}

// Ödeme detaylarını getirme
export async function getPaymentDetails(paymentId: string) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        booking: {
          include: {
            student: true,
            teacher: true,
            availabilitySlot: true,
          },
        },
      },
    });

    return payment;
  } catch (error) {
    console.error("Ödeme detayları getirme hatası:", error);
    return null;
  }
}

// Geri ödeme işlemi
export async function processRefund(
  paymentIntentId: string,
  amount?: number,
  reason?: string
): Promise<{ success: boolean; error?: string; refundId?: string }> {
  try {
    // Refund oluştur
    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      amount: amount ? toStripeAmount(amount) : undefined,
      reason: reason as Stripe.RefundCreateParams.Reason,
    });

    // Payment kaydını güncelle
    await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntentId },
      data: { status: "refunded" },
    });

    // Booking durumunu güncelle
    const payment = await prisma.payment.findUnique({
      where: { stripePaymentIntentId: paymentIntentId },
      include: { booking: true },
    });

    if (payment) {
      await prisma.booking.update({
        where: { id: payment.bookingId },
        data: { status: "CANCELLED" },
      });

      // Availability slot'u tekrar müsait yap
      await prisma.availabilitySlot.update({
        where: { id: payment.booking.availabilitySlotId },
        data: { isBooked: false },
      });
    }

    revalidatePath("/dashboard");
    revalidatePath("/teacher");
    revalidatePath("/student");

    return {
      success: true,
      refundId: refund.id,
    };
  } catch (error) {
    console.error("Geri ödeme hatası:", error);
    return {
      success: false,
      error: "Geri ödeme işlemi sırasında bir hata oluştu.",
    };
  }
}
