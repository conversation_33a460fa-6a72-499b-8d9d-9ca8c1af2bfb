"use server";

import { teacherBookingFormSchema, TeacherBookingFormValues } from "@/lib/formValidationSchemas"; // TeacherBookingFormValues import edildi
import { prisma } from '@/lib/prisma';
import { Prisma } from "@prisma/client";
import { Resend } from 'resend'; // Eklendi

// RESEND_API_KEY kontrolü
if (!process.env.RESEND_API_KEY) {
  console.warn("RESEND_API_KEY ortam değişkeni ayarlanmamış. E-posta gönderimi çalışmayacak.");
  // throw new Error("RESEND_API_KEY is not set. Email functionality will be disabled.");
}
const resend = new Resend(process.env.RESEND_API_KEY);
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || 'AlmancaABC <<EMAIL>>';


export type BookingFormState = {
  message: string;
  fields?: Record<string, string>;
  issues?: string[];
  success: boolean;
  bookingId?: string;
  name?: string;
  email?: string;
  phone?: string;
  courseType?: string;
  courseTitle?: string;
  coursePrice?: string;
  teacherId?: string;
  teacherName?: string;
  bookingDateTime?: string;
};

export async function submitBookingForm(
  prevState: BookingFormState,
  data: FormData
): Promise<BookingFormState> {
  const formData = Object.fromEntries(data);
  const parsed = teacherBookingFormSchema.safeParse(formData);

  if (!parsed.success) {
    const issues = parsed.error.issues.map((issue) => issue.message);
    return {
      message: "Form verileri geçersiz. Lütfen hataları düzeltin.",
      fields: formData as Record<string, string>,
      issues,
      success: false,
    };
  }

  const {
    studentId,
    teacherId,
    availabilitySlotId,
    lessonTime: lessonTimeString, // Renamed to avoid conflict
    durationMinutes,
    pricePaid: pricePaidString, // Renamed to avoid conflict
    notes,
    name: studentNameFromForm, // For email
    email: studentEmailFromForm, // For email
    courseTitle, // For email
    message: studentMessage // Potansiyel olarak notlara eklenebilir
  } = parsed.data;

  const lessonTimeDate = new Date(lessonTimeString);
  const finalNotes = notes ? (studentMessage ? `${notes} - Student message: ${studentMessage}` : notes) : studentMessage;
  const pricePaidDecimal = new Prisma.Decimal(pricePaidString);

  let newBooking; // To store created booking

  try {
    newBooking = await prisma.booking.create({
      data: {
        studentId: studentId,
        teacherId: teacherId,
        availabilitySlotId: availabilitySlotId,
        lessonTime: lessonTimeDate,
        durationMinutes: durationMinutes,
        pricePaid: pricePaidDecimal,
        status: "CONFIRMED",
        lessonType: courseTitle || 'General', // courseTitle'ı lessonType olarak kullanabiliriz
        notes: finalNotes,
      },
    });

    await prisma.availabilitySlot.update({
      where: { id: availabilitySlotId },
      data: { isBooked: true },
    });

    // E-posta gönderme işlemleri
    if (process.env.RESEND_API_KEY) {
      try {
        const teacher = await prisma.teacher.findUnique({ where: { id: teacherId }, select: { email: true, name: true, firstName: true, lastName: true } });
        const student = await prisma.student.findUnique({ where: { id: studentId }, select: { email: true, name: true, firstName: true, lastName: true } });

        const studentEmail = student?.email || studentEmailFromForm;
        const studentFullName = student?.name || student?.firstName || studentNameFromForm || 'Değerli Öğrencimiz';
        const teacherFullName = teacher?.name || teacher?.firstName || 'Değerli Öğretmenimiz';
        const teacherEmail = teacher?.email;


        if (studentEmail) {
          await sendBookingConfirmationEmailToStudent({
            ...parsed.data,
            bookingId: newBooking.id,
            studentName: studentFullName,
            teacherName: teacherFullName,
            studentEmail: studentEmail, // explicit pass
            lessonTime: lessonTimeDate, // pass Date object
            pricePaid: pricePaidDecimal, // pass Decimal object
            durationMinutes: durationMinutes,
            courseTitle: courseTitle || 'Ders'
          });
        } else {
          console.warn(`Öğrenci e-postası bulunamadı (Booking ID: ${newBooking.id}), onay e-postası gönderilemedi.`);
        }

        if (teacherEmail) {
          await sendNewBookingNotificationToTeacher({
            ...parsed.data,
            bookingId: newBooking.id,
            studentName: studentFullName,
            teacherName: teacherFullName,
            teacherEmail: teacherEmail, // explicit pass
            lessonTime: lessonTimeDate, // pass Date object
            durationMinutes: durationMinutes,
            courseTitle: courseTitle || 'Ders'
          });
        } else {
          console.warn(`Öğretmen e-postası bulunamadı (Booking ID: ${newBooking.id}), bildirim e-postası gönderilemedi.`);
        }

      } catch (emailError) {
        console.error("Rezervasyon sonrası e-posta gönderilirken hata oluştu:", emailError);
        // Email gönderme hatası ana işlemi etkilememeli, sadece loglanmalı.
      }
    } else {
        console.log(`RESEND_API_KEY ayarlanmadığı için e-postalar gönderilmedi. Booking ID: ${newBooking.id}`);
    }

    return {
      message: `Rezervasyonunuz başarıyla oluşturuldu (ID: ${newBooking.id}). Detaylar e-posta adresinize gönderilmiştir (eğer e-posta ayarları tamamsa).`,
      success: true,
      bookingId: newBooking.id,
    };

  } catch (error) {
    console.error("Rezervasyon oluşturulurken hata:", error);
    let errorMessage = "Rezervasyon oluşturulurken bir hata oluştu. Lütfen daha sonra tekrar deneyin.";
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        if (error.meta?.target === 'Booking_availabilitySlotId_key') {
             errorMessage = "Bu zaman aralığı zaten rezerve edilmiş. Lütfen farklı bir zaman seçin.";
        } else {
            errorMessage = "Veri girişi hatası. Lütfen bilgileri kontrol edin (Örn: Bu kayıt zaten mevcut olabilir).";
        }
      } else if (error.code === 'P2003') {
         if (error.meta?.field_name === 'Booking_studentId_fkey' || error.meta?.field_name === 'StudentBookings') {
            errorMessage = "Belirtilen öğrenci bulunamadı.";
         } else if (error.meta?.field_name === 'Booking_teacherId_fkey' || error.meta?.field_name === 'TeacherBookings') {
            errorMessage = "Belirtilen öğretmen bulunamadı.";
         } else if (error.meta?.field_name === 'Booking_availabilitySlotId_fkey') {
            errorMessage = "Belirtilen zaman aralığı bulunamadı.";
         }
      } else if (error.code === 'P2025') {
         errorMessage = "Rezervasyon yapılmak istenen zaman aralığı bulunamadı veya artık geçerli değil.";
      }
    }
    return {
      message: errorMessage,
      success: false,
      fields: formData as Record<string, string>,
    };
  }
}

// Tipleri güncelle
interface BookingEmailData {
  bookingId: string;
  studentName: string;
  teacherName: string;
  lessonTime: Date;
  durationMinutes: number;
  courseTitle: string;
  studentEmail?: string; // Öğrenci e-postası için
  teacherEmail?: string; // Öğretmen e-postası için
  pricePaid?: Prisma.Decimal; // Öğrenci e-postası için
  notes?: string; // Öğretmen e-postası için
}


async function sendBookingConfirmationEmailToStudent(data: BookingEmailData & { studentEmail: string }) {
  if (!process.env.RESEND_API_KEY) return;
  const subject = `AlmancaABC - Ders Rezervasyon Onayı (ID: ${data.bookingId})`;
  const htmlBody = `
    <p>Merhaba ${data.studentName},</p>
    <p>AlmancaABC üzerinden yapmış olduğunuz ders rezervasyonunuz onaylanmıştır.</p>
    <p><strong>Rezervasyon Detayları:</strong></p>
    <ul>
      <li>Rezervasyon ID: ${data.bookingId}</li>
      <li>Öğretmen: ${data.teacherName}</li>
      <li>Ders: ${data.courseTitle}</li>
      <li>Tarih ve Saat: ${data.lessonTime.toLocaleString('tr-TR')}</li>
      <li>Süre: ${data.durationMinutes} dakika</li>
      ${data.pricePaid ? `<li>Ödenen Ücret: ${data.pricePaid.toString()} EUR</li>` : ''}
    </ul>
    <p>Dersinize zamanında katılmayı unutmayın!</p>
    <p>İyi dersler!</p>
    <p>AlmancaABC Ekibi</p>
  `;

  try {
    await resend.emails.send({
      from: RESEND_FROM_EMAIL,
      to: [data.studentEmail],
      subject: subject,
      html: htmlBody,
    });
    console.log(`Rezervasyon onay e-postası öğrenciye gönderildi: ${data.studentEmail} (Booking ID: ${data.bookingId})`);
  } catch (error) {
    console.error(`Öğrenciye onay e-postası gönderilemedi (Booking ID: ${data.bookingId}):`, error);
  }
}

async function sendNewBookingNotificationToTeacher(data: BookingEmailData & { teacherEmail: string }) {
  if (!process.env.RESEND_API_KEY) return;
  const subject = `AlmancaABC - Yeni Ders Rezervasyonu (ID: ${data.bookingId})`;
  const htmlBody = `
    <p>Merhaba ${data.teacherName},</p>
    <p>Yeni bir ders rezervasyonu aldınız.</p>
    <p><strong>Rezervasyon Detayları:</strong></p>
    <ul>
      <li>Rezervasyon ID: ${data.bookingId}</li>
      <li>Öğrenci: ${data.studentName}</li>
      <li>Ders: ${data.courseTitle}</li>
      <li>Tarih ve Saat: ${data.lessonTime.toLocaleString('tr-TR')}</li>
      <li>Süre: ${data.durationMinutes} dakika</li>
      ${data.notes ? `<li>Öğrenci Notu: ${data.notes}</li>` : ''}
    </ul>
    <p>Lütfen takviminizi kontrol edin ve hazırlıklarınızı yapın.</p>
    <p>İyi dersler!</p>
    <p>AlmancaABC Ekibi</p>
  `;

  try {
    await resend.emails.send({
      from: RESEND_FROM_EMAIL,
      to: [data.teacherEmail],
      subject: subject,
      html: htmlBody,
    });
    console.log(`Yeni rezervasyon bildirimi öğretmene gönderildi: ${data.teacherEmail} (Booking ID: ${data.bookingId})`);
  } catch (error) {
    console.error(`Öğretmene yeni rezervasyon bildirimi gönderilemedi (Booking ID: ${data.bookingId}):`, error);
  }
}